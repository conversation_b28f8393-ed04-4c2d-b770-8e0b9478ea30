.blog-navigation {
  padding: 3rem 0;
  border-top: 1px solid #e5e7eb;
}

.blog-navigation__container {
  max-width: 72rem;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 1024px) {
  .blog-navigation__container {
    grid-template-columns: 1fr 1fr 1fr;
    padding: 0 2rem;
  }
}

/* Navigation Items */
.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-item--previous {
  justify-content: center;
}

@media (min-width: 1024px) {
  .nav-item--previous {
    justify-content: flex-start;
  }
}

.nav-item--center {
  order: -1;
}

@media (min-width: 1024px) {
  .nav-item--center {
    order: 0;
  }
}

.nav-item--next {
  justify-content: center;
}

@media (min-width: 1024px) {
  .nav-item--next {
    justify-content: flex-end;
  }
}

/* Navigation Links */
.nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  max-width: 28rem;
  width: 100%;
  text-decoration: none;
  color: inherit;
}

.nav-link:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.nav-link--center {
  flex-direction: column;
  gap: 0.5rem;
  max-width: 18rem;
  background-color: #eff6ff;
  border-color: #bfdbfe;
}

.nav-link--center:hover {
  background-color: #dbeafe;
}

/* Navigation Direction */
.nav-direction {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  transition: color 0.2s ease;
}

.nav-link:hover .nav-direction {
  color: #2563eb;
}

.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Navigation Content */
.nav-content {
  flex: 1;
  min-width: 0;
}

.nav-item--next .nav-content {
  text-align: right;
}

.nav-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  transition: color 0.2s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.nav-link:hover .nav-title {
  color: #2563eb;
}

.nav-excerpt {
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.nav-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.nav-item--next .nav-meta {
  justify-content: flex-end;
}

/* Placeholder */
.nav-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  color: #9ca3af;
  font-size: 0.875rem;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .blog-navigation__container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nav-item--previous,
  .nav-item--next {
    justify-content: center;
  }

  .nav-item--next .nav-content {
    text-align: left;
  }

  .nav-item--next .nav-meta {
    justify-content: flex-start;
  }

  .nav-link {
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .nav-link {
    padding: 1rem;
    gap: 0.75rem;
  }

  .nav-title {
    font-size: 1rem;
  }

  .nav-excerpt {
    font-size: 0.75rem;
  }
}

/* Hover Effects */
.nav-link:hover .nav-icon {
  transform: scale(1.1);
}

.nav-item--previous .nav-link:hover .nav-icon {
  transform: translateX(-0.25rem) scale(1.1);
}

.nav-item--next .nav-link:hover .nav-icon {
  transform: translateX(0.25rem) scale(1.1);
}

/* Focus States */
.nav-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Utility Classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation */
.nav-link {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .blog-navigation {
    border-top-color: #374151;
  }

  .nav-link {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .nav-direction {
    color: #9ca3af;
  }

  .nav-link:hover .nav-direction {
    color: #60a5fa;
  }

  .nav-title {
    color: #f9fafb;
  }

  .nav-link:hover .nav-title {
    color: #60a5fa;
  }

  .nav-excerpt {
    color: #d1d5db;
  }

  .nav-meta {
    color: #9ca3af;
  }

  .nav-placeholder {
    color: #6b7280;
  }

  .nav-link--center {
    background-color: rgba(30, 58, 138, 0.1);
    border-color: rgba(147, 197, 253, 0.5);
  }

  .nav-link--center:hover {
    background-color: rgba(30, 58, 138, 0.2);
  }
}
